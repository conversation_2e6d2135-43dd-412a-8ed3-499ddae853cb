#!/usr/bin/env python3
"""
Demo script for 2FA setup in MTBRMG ERP System
This script demonstrates the complete 2FA setup process
"""

import requests
import json
import qrcode
from io import BytesIO
import base64

# Configuration
BASE_URL = "http://localhost:8000"
API_V1_URL = f"{BASE_URL}/api/v1"

# Test credentials
CREDENTIALS = {
    "username": "<EMAIL>",
    "password": "demo123"
}

class MTBRMGDemo:
    def __init__(self):
        self.session = requests.Session()
        self.access_token = None
        
    def authenticate(self):
        """Authenticate and get access token"""
        print("🔐 Authenticating...")
        response = self.session.post(
            f"{API_V1_URL}/auth/login/",
            json=CREDENTIALS,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            self.access_token = data.get("access")
            self.session.headers.update({
                "Authorization": f"Bearer {self.access_token}"
            })
            print("✅ Authentication successful!")
            return True
        else:
            print(f"❌ Authentication failed: {response.status_code}")
            return False
    
    def check_2fa_status(self):
        """Check current 2FA status"""
        print("\n📱 Checking 2FA status...")
        response = self.session.get(f"{API_V1_URL}/auth/2fa/status/")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 2FA Status: {json.dumps(data, indent=2)}")
            return data
        else:
            print(f"❌ Failed to check 2FA status: {response.status_code}")
            return None
    
    def setup_2fa(self):
        """Setup 2FA and get QR code"""
        print("\n🔧 Setting up 2FA...")
        response = self.session.post(f"{API_V1_URL}/auth/2fa/setup/")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 2FA setup successful!")
            
            # Display QR code information
            if 'qr_code' in data:
                print(f"📱 QR Code URL: {data['qr_code']}")
                print("📝 Scan this QR code with Google Authenticator or similar app")
                
                # Generate QR code image (optional)
                try:
                    qr = qrcode.QRCode(version=1, box_size=10, border=5)
                    qr.add_data(data['qr_code'])
                    qr.make(fit=True)
                    
                    print("🖼️  QR Code generated successfully")
                except Exception as e:
                    print(f"⚠️  Could not generate QR code image: {e}")
            
            if 'backup_codes' in data:
                print(f"\n🔑 Backup codes (save these safely):")
                for i, code in enumerate(data['backup_codes'], 1):
                    print(f"  {i}. {code}")
            
            return data
        else:
            print(f"❌ 2FA setup failed: {response.status_code}")
            if response.text:
                print(f"Error: {response.text}")
            return None
    
    def verify_2fa(self, token):
        """Verify 2FA token"""
        print(f"\n🔍 Verifying 2FA token: {token}")
        response = self.session.post(
            f"{API_V1_URL}/auth/2fa/verify/",
            json={"token": token}
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 2FA verification successful!")
            return data
        else:
            print(f"❌ 2FA verification failed: {response.status_code}")
            return None
    
    def test_api_access(self):
        """Test API access with authentication"""
        print("\n🌐 Testing API access...")
        
        # Test clients endpoint
        response = self.session.get(f"{API_V1_URL}/clients/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Clients API: {len(data.get('results', []))} clients found")
        else:
            print(f"❌ Clients API failed: {response.status_code}")
        
        # Test projects endpoint
        response = self.session.get(f"{API_V1_URL}/projects/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Projects API: {len(data.get('results', []))} projects found")
        else:
            print(f"❌ Projects API failed: {response.status_code}")
    
    def demo_complete_flow(self):
        """Demonstrate the complete 2FA setup flow"""
        print("🚀 MTBRMG ERP System - 2FA Demo")
        print("=" * 50)
        
        # Step 1: Authenticate
        if not self.authenticate():
            return
        
        # Step 2: Check initial 2FA status
        initial_status = self.check_2fa_status()
        
        # Step 3: Setup 2FA if not already enabled
        if initial_status and not initial_status.get('mfa_enabled', False):
            setup_result = self.setup_2fa()
            
            if setup_result:
                print("\n📋 2FA Setup Complete!")
                print("Next steps:")
                print("1. Scan the QR code with your authenticator app")
                print("2. Enter the 6-digit code from your app to verify")
                print("3. Save the backup codes in a secure location")
                
                # Prompt for verification (in a real scenario)
                print("\n💡 To verify 2FA, call verify_2fa(token) with your 6-digit code")
        else:
            print("ℹ️  2FA is already enabled for this account")
        
        # Step 4: Test API access
        self.test_api_access()
        
        # Step 5: Final status check
        print("\n📊 Final 2FA status:")
        self.check_2fa_status()
        
        print("\n🎉 Demo completed successfully!")
        print("The MTBRMG ERP System is now secured with 2FA")

def main():
    """Main demo function"""
    demo = MTBRMGDemo()
    demo.demo_complete_flow()

if __name__ == "__main__":
    main()
