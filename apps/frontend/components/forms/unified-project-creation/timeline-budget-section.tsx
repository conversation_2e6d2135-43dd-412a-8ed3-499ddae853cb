'use client';

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar, DollarSign, Clock } from 'lucide-react';

interface ProjectData {
  start_date: string;
  deadline: string;
  budget: number;
  payment_terms: string;
  [key: string]: any;
}

interface TimelineBudgetSectionProps {
  data: ProjectData;
  onChange: (data: Partial<ProjectData>) => void;
  errors: { [key: string]: string };
}

const PAYMENT_TERMS = [
  { value: '50% upfront, 50% on delivery', label: '50% مقدم، 50% عند التسليم' },
  { value: '30% upfront, 70% on delivery', label: '30% مقدم، 70% عند التسليم' },
  { value: 'Full payment upfront', label: 'دفع كامل مقدم' },
  { value: 'Monthly installments', label: 'أقساط شهرية' },
  { value: 'Custom terms', label: 'شروط مخصصة' }
];

export function TimelineBudgetSection({ data, onChange, errors }: TimelineBudgetSectionProps) {
  
  const handleInputChange = (field: keyof ProjectData, value: string | number) => {
    onChange({ [field]: value });
  };

  // Calculate estimated duration
  const calculateDuration = () => {
    if (data.start_date && data.deadline) {
      const startDate = new Date(data.start_date);
      const endDate = new Date(data.deadline);
      const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      const weeks = Math.floor(diffDays / 7);
      const days = diffDays % 7;
      
      if (weeks > 0) {
        return `${weeks} أسبوع${weeks > 1 ? ' و' : ''}${days > 0 ? ` ${days} يوم` : ''}`;
      } else {
        return `${days} يوم`;
      }
    }
    return null;
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Get today's date for min date validation
  const today = new Date().toISOString().split('T')[0];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          الجدول الزمني والميزانية
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Timeline Section */}
        <div>
          <h4 className="font-medium text-gray-900 mb-4">الجدول الزمني</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="start_date">تاريخ البداية *</Label>
              <Input
                id="start_date"
                type="date"
                value={data.start_date}
                onChange={(e) => handleInputChange('start_date', e.target.value)}
                min={today}
                className={errors.project_start_date ? 'border-red-500' : ''}
              />
              {errors.project_start_date && (
                <p className="text-sm text-red-500 mt-1">{errors.project_start_date}</p>
              )}
            </div>

            <div>
              <Label htmlFor="deadline">الموعد النهائي *</Label>
              <Input
                id="deadline"
                type="date"
                value={data.deadline}
                onChange={(e) => handleInputChange('deadline', e.target.value)}
                min={data.start_date || today}
                className={errors.project_deadline ? 'border-red-500' : ''}
              />
              {errors.project_deadline && (
                <p className="text-sm text-red-500 mt-1">{errors.project_deadline}</p>
              )}
            </div>
          </div>

          {/* Duration Display */}
          {calculateDuration() && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2 text-blue-800">
                <Clock className="h-4 w-4" />
                <span className="font-medium">المدة المقدرة:</span>
                <span>{calculateDuration()}</span>
              </div>
            </div>
          )}
        </div>

        {/* Budget Section */}
        <div>
          <h4 className="font-medium text-gray-900 mb-4">الميزانية</h4>
          <div className="space-y-4">
            <div>
              <Label htmlFor="budget">ميزانية المشروع (جنيه مصري) *</Label>
              <div className="relative">
                <DollarSign className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  id="budget"
                  type="number"
                  min="0"
                  step="100"
                  value={data.budget || ''}
                  onChange={(e) => handleInputChange('budget', parseFloat(e.target.value) || 0)}
                  placeholder="أدخل ميزانية المشروع"
                  className={`pr-10 ${errors.project_budget ? 'border-red-500' : ''}`}
                />
              </div>
              {errors.project_budget && (
                <p className="text-sm text-red-500 mt-1">{errors.project_budget}</p>
              )}
              
              {/* Budget Display */}
              {data.budget > 0 && (
                <p className="text-sm text-gray-600 mt-1">
                  الميزانية: <span className="font-medium">{formatCurrency(data.budget)}</span>
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="payment_terms">شروط الدفع</Label>
              <Select 
                value={data.payment_terms} 
                onValueChange={(value) => handleInputChange('payment_terms', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر شروط الدفع" />
                </SelectTrigger>
                <SelectContent>
                  {PAYMENT_TERMS.map((term) => (
                    <SelectItem key={term.value} value={term.value}>
                      {term.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Budget Breakdown (if applicable) */}
        {data.budget > 0 && data.payment_terms && (
          <div className="p-4 bg-gray-50 rounded-lg">
            <h5 className="font-medium text-gray-900 mb-3">تفصيل المدفوعات</h5>
            <div className="space-y-2 text-sm">
              {data.payment_terms === '50% upfront, 50% on delivery' && (
                <>
                  <div className="flex justify-between">
                    <span>الدفعة المقدمة (50%):</span>
                    <span className="font-medium">{formatCurrency(data.budget * 0.5)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>الدفعة النهائية (50%):</span>
                    <span className="font-medium">{formatCurrency(data.budget * 0.5)}</span>
                  </div>
                </>
              )}
              
              {data.payment_terms === '30% upfront, 70% on delivery' && (
                <>
                  <div className="flex justify-between">
                    <span>الدفعة المقدمة (30%):</span>
                    <span className="font-medium">{formatCurrency(data.budget * 0.3)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>الدفعة النهائية (70%):</span>
                    <span className="font-medium">{formatCurrency(data.budget * 0.7)}</span>
                  </div>
                </>
              )}
              
              {data.payment_terms === 'Full payment upfront' && (
                <div className="flex justify-between">
                  <span>الدفع الكامل مقدماً:</span>
                  <span className="font-medium">{formatCurrency(data.budget)}</span>
                </div>
              )}
              
              {data.payment_terms === 'Monthly installments' && calculateDuration() && (
                <div className="text-gray-600">
                  <p>سيتم تحديد قيمة الأقساط الشهرية بناءً على مدة المشروع</p>
                </div>
              )}
              
              {data.payment_terms === 'Custom terms' && (
                <div className="text-gray-600">
                  <p>سيتم تحديد شروط الدفع المخصصة مع العميل</p>
                </div>
              )}
              
              <div className="border-t pt-2 mt-2">
                <div className="flex justify-between font-medium">
                  <span>إجمالي المشروع:</span>
                  <span>{formatCurrency(data.budget)}</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
