'use client';

import { UnifiedLayout } from '@/components/layout';
import { UnifiedProjectCreationForm } from '@/components/forms/unified-project-creation-form';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { FolderOpen } from 'lucide-react';

export default function NewProjectPage() {
  return (
    <UnifiedLayout>
      <div className="max-w-6xl mx-auto py-8">
        {/* Breadcrumb Navigation */}
        <Breadcrumb className="mb-6">
          <BreadcrumbItem>
            <BreadcrumbLink href="/founder-dashboard">لوحة التحكم</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/founder-dashboard/projects">المشاريع</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbPage>إضافة مشروع جديد</BreadcrumbPage>
        </Breadcrumb>

        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            <FolderOpen className="h-8 w-8 text-purple-600" />
            إضافة مشروع جديد
          </h1>
          <p className="text-gray-600 mt-2">
            اختر طريقة إنشاء المشروع وأكمل المعلومات المطلوبة
          </p>
        </div>

        {/* Main Form */}
        <UnifiedProjectCreationForm />
      </div>
    </UnifiedLayout>
  );
}
