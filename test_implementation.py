#!/usr/bin/env python3
"""
Test script for MTBRMG ERP System Implementation
Tests the new features including 2FA, caching, API versioning, and security enhancements
"""

import requests
import json
import time
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000"
API_V1_URL = f"{BASE_URL}/api/v1"
API_LEGACY_URL = f"{BASE_URL}/api"

# Test credentials (using the founder credentials from memory)
TEST_CREDENTIALS = {
    "username": "<EMAIL>",
    "password": "demo123"
}

class MTBRMGTestSuite:
    def __init__(self):
        self.session = requests.Session()
        self.access_token = None
        self.refresh_token = None
        
    def log(self, message, status="INFO"):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {status}: {message}")
    
    def test_health_check(self):
        """Test the health check endpoint"""
        self.log("Testing health check endpoint...")
        try:
            response = self.session.get(f"{BASE_URL}/api/health/")
            if response.status_code == 200:
                data = response.json()
                self.log(f"Health check passed: {data}", "SUCCESS")
                return True
            else:
                self.log(f"Health check failed: {response.status_code}", "ERROR")
                return False
        except Exception as e:
            self.log(f"Health check error: {str(e)}", "ERROR")
            return False
    
    def test_api_versioning(self):
        """Test API versioning endpoints"""
        self.log("Testing API versioning...")
        
        # Test v1 endpoint
        try:
            response = self.session.get(f"{API_V1_URL}/auth/2fa/status/")
            if response.status_code == 401:  # Expected - requires auth
                self.log("API v1 versioning working (requires auth)", "SUCCESS")
                v1_works = True
            else:
                self.log(f"API v1 unexpected response: {response.status_code}", "WARNING")
                v1_works = False
        except Exception as e:
            self.log(f"API v1 error: {str(e)}", "ERROR")
            v1_works = False
        
        # Test legacy endpoint
        try:
            response = self.session.get(f"{API_LEGACY_URL}/auth/2fa/status/")
            if response.status_code == 401:  # Expected - requires auth
                self.log("Legacy API working (requires auth)", "SUCCESS")
                legacy_works = True
            else:
                self.log(f"Legacy API unexpected response: {response.status_code}", "WARNING")
                legacy_works = False
        except Exception as e:
            self.log(f"Legacy API error: {str(e)}", "ERROR")
            legacy_works = False
        
        return v1_works and legacy_works
    
    def test_authentication(self):
        """Test authentication and get tokens"""
        self.log("Testing authentication...")
        try:
            response = self.session.post(
                f"{API_V1_URL}/auth/login/",
                json=TEST_CREDENTIALS,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                self.access_token = data.get("access")
                self.refresh_token = data.get("refresh")
                
                # Set authorization header for future requests
                self.session.headers.update({
                    "Authorization": f"Bearer {self.access_token}"
                })
                
                self.log("Authentication successful", "SUCCESS")
                return True
            else:
                self.log(f"Authentication failed: {response.status_code} - {response.text}", "ERROR")
                return False
        except Exception as e:
            self.log(f"Authentication error: {str(e)}", "ERROR")
            return False
    
    def test_2fa_endpoints(self):
        """Test 2FA endpoints"""
        self.log("Testing 2FA endpoints...")
        
        if not self.access_token:
            self.log("No access token available for 2FA testing", "ERROR")
            return False
        
        # Test 2FA status
        try:
            response = self.session.get(f"{API_V1_URL}/auth/2fa/status/")
            if response.status_code == 200:
                data = response.json()
                self.log(f"2FA status: {data}", "SUCCESS")
                status_works = True
            else:
                self.log(f"2FA status failed: {response.status_code}", "ERROR")
                status_works = False
        except Exception as e:
            self.log(f"2FA status error: {str(e)}", "ERROR")
            status_works = False
        
        # Test 2FA setup (this will fail if already setup, which is expected)
        try:
            response = self.session.post(f"{API_V1_URL}/auth/2fa/setup/")
            if response.status_code in [200, 400]:  # 400 if already setup
                self.log("2FA setup endpoint working", "SUCCESS")
                setup_works = True
            else:
                self.log(f"2FA setup unexpected response: {response.status_code}", "WARNING")
                setup_works = False
        except Exception as e:
            self.log(f"2FA setup error: {str(e)}", "ERROR")
            setup_works = False
        
        return status_works and setup_works
    
    def test_api_throttling(self):
        """Test API throttling"""
        self.log("Testing API throttling...")
        
        # Make multiple rapid requests to test throttling
        throttle_endpoint = f"{API_V1_URL}/auth/login/"
        throttled = False
        
        for i in range(10):
            try:
                response = self.session.post(
                    throttle_endpoint,
                    json={"username": "invalid", "password": "invalid"},
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code == 429:  # Too Many Requests
                    self.log("API throttling working correctly", "SUCCESS")
                    throttled = True
                    break
                elif response.status_code == 400:  # Invalid credentials (expected)
                    continue
                else:
                    self.log(f"Unexpected response during throttling test: {response.status_code}", "WARNING")
            except Exception as e:
                self.log(f"Throttling test error: {str(e)}", "ERROR")
                break
            
            time.sleep(0.1)  # Small delay between requests
        
        if not throttled:
            self.log("API throttling not triggered (may need more requests)", "WARNING")
        
        return True  # Don't fail the test if throttling isn't triggered
    
    def test_caching_performance(self):
        """Test caching performance"""
        self.log("Testing caching performance...")
        
        if not self.access_token:
            self.log("No access token available for caching testing", "ERROR")
            return False
        
        # Test clients endpoint for caching
        endpoint = f"{API_V1_URL}/clients/"
        
        # First request (should hit database)
        start_time = time.time()
        try:
            response1 = self.session.get(endpoint)
            first_request_time = time.time() - start_time
            
            if response1.status_code == 200:
                self.log(f"First request time: {first_request_time:.3f}s", "INFO")
                
                # Second request (should hit cache)
                start_time = time.time()
                response2 = self.session.get(endpoint)
                second_request_time = time.time() - start_time
                
                if response2.status_code == 200:
                    self.log(f"Second request time: {second_request_time:.3f}s", "INFO")
                    
                    # Check if second request is faster (indicating caching)
                    if second_request_time < first_request_time:
                        self.log("Caching appears to be working (faster second request)", "SUCCESS")
                    else:
                        self.log("Caching may not be working (second request not faster)", "WARNING")
                    
                    return True
                else:
                    self.log(f"Second request failed: {response2.status_code}", "ERROR")
                    return False
            else:
                self.log(f"First request failed: {response1.status_code}", "ERROR")
                return False
        except Exception as e:
            self.log(f"Caching test error: {str(e)}", "ERROR")
            return False
    
    def test_security_headers(self):
        """Test security headers and responses"""
        self.log("Testing security headers...")
        
        try:
            response = self.session.get(f"{BASE_URL}/api/health/")
            headers = response.headers
            
            # Check for security headers
            security_checks = {
                "Content-Type": "application/json" in headers.get("Content-Type", ""),
                "Server_Hidden": "Server" not in headers,  # Server header should be hidden
            }
            
            passed_checks = sum(security_checks.values())
            total_checks = len(security_checks)
            
            self.log(f"Security checks passed: {passed_checks}/{total_checks}", "INFO")
            
            for check, passed in security_checks.items():
                status = "SUCCESS" if passed else "WARNING"
                self.log(f"  {check}: {'PASS' if passed else 'FAIL'}", status)
            
            return passed_checks >= total_checks // 2  # At least half should pass
        except Exception as e:
            self.log(f"Security headers test error: {str(e)}", "ERROR")
            return False
    
    def run_all_tests(self):
        """Run all tests"""
        self.log("Starting MTBRMG ERP Implementation Test Suite", "INFO")
        self.log("=" * 60, "INFO")
        
        tests = [
            ("Health Check", self.test_health_check),
            ("API Versioning", self.test_api_versioning),
            ("Authentication", self.test_authentication),
            ("2FA Endpoints", self.test_2fa_endpoints),
            ("API Throttling", self.test_api_throttling),
            ("Caching Performance", self.test_caching_performance),
            ("Security Headers", self.test_security_headers),
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            self.log(f"\n--- Running {test_name} Test ---", "INFO")
            try:
                result = test_func()
                results[test_name] = result
                status = "PASSED" if result else "FAILED"
                self.log(f"{test_name} Test: {status}", "SUCCESS" if result else "ERROR")
            except Exception as e:
                results[test_name] = False
                self.log(f"{test_name} Test: FAILED - {str(e)}", "ERROR")
        
        # Summary
        self.log("\n" + "=" * 60, "INFO")
        self.log("TEST SUMMARY", "INFO")
        self.log("=" * 60, "INFO")
        
        passed = sum(results.values())
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            self.log(f"{test_name}: {status}", "SUCCESS" if result else "ERROR")
        
        self.log(f"\nOverall: {passed}/{total} tests passed", "INFO")
        
        if passed == total:
            self.log("🎉 ALL TESTS PASSED! Implementation is working correctly.", "SUCCESS")
        elif passed >= total * 0.8:
            self.log("✅ Most tests passed. Implementation is mostly working.", "SUCCESS")
        else:
            self.log("⚠️  Some tests failed. Please check the implementation.", "WARNING")
        
        return passed, total


if __name__ == "__main__":
    test_suite = MTBRMGTestSuite()
    passed, total = test_suite.run_all_tests()
    
    # Exit with appropriate code
    exit(0 if passed == total else 1)
